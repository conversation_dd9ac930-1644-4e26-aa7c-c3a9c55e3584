import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../../../blocs/table/table_bloc.dart';
import '../../../../blocs/table/table_event.dart';
import '../../../../blocs/table/table_state.dart';
import '../../../../models/bill_model.dart';

void handleViewBill(BuildContext context, Map<String, dynamic> table) {
  final tableBloc = context.read<TableBloc>();
  tableBloc.add(ViewTableBill(tableId: table['id'].toString()));

  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => BlocBuilder<TableBloc, TableState>(
      builder: (context, state) {
        if (state.isFetchingBill) {
          return const Dialog(
            backgroundColor: Colors.transparent,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (state.error != null) {
          return AlertDialog(
            backgroundColor: Colors.grey[900],
            title: Text(
              'Error',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
            content: Text(
              state.error!,
              style: GoogleFonts.poppins(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'OK',
                  style: GoogleFonts.poppins(color: Colors.blue),
                ),
              ),
            ],
          );
        }

        if (state.currentTableBill != null) {
          return TableBillDialog(
            billResponse: state.currentTableBill!,
            tableName: table['tableNumber'] ?? 'Unknown Table',
          );
        }

        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            'No Bills Found',
            style: GoogleFonts.poppins(color: Colors.white),
          ),
          content: Text(
            'No bills found for this table.',
            style: GoogleFonts.poppins(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'OK',
                style: GoogleFonts.poppins(color: Colors.blue),
              ),
            ),
          ],
        );
      },
    ),
  );
}

class TableBillDialog extends StatelessWidget {
  final TableBillResponse billResponse;
  final String tableName;

  const TableBillDialog({
    Key? key,
    required this.billResponse,
    required this.tableName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bills = billResponse.data.bills;
    final summary = billResponse.data.summary;

    return Dialog(
      backgroundColor: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Bills for $tableName',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Summary
            _buildSummaryCard(summary),
            const SizedBox(height: 20),

            // Bills List
            Expanded(
              child: _buildBillsList(bills),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(BillSummary summary) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem('Total Bills', summary.totalBills.toString()),
          _buildSummaryItem(
              'Total Amount', '\$${summary.totalAmount.toStringAsFixed(2)}'),
          _buildSummaryItem('Pending Amount',
              '\$${summary.pendingAmount.toStringAsFixed(2)}'),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildBillsList(BillsByStatus bills) {
    final allBills = bills.allBills;

    if (allBills.isEmpty) {
      return Center(
        child: Text(
          'No bills found for this table',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 16,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: allBills.length,
      itemBuilder: (context, index) {
        final bill = allBills[index];
        return _buildBillCard(bill);
      },
    );
  }

  Widget _buildBillCard(TableBill bill) {
    final statusColor = _getStatusColor(bill.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bill Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                bill.billNumber,
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: statusColor),
                ),
                child: Text(
                  bill.status.toUpperCase(),
                  style: GoogleFonts.poppins(
                    color: statusColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Order Details
          if (bill.orderDetails != null) ...[
            Row(
              children: [
                Icon(Icons.receipt, color: Colors.white70, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Order: ${bill.orderDetails!.orderCode}',
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Amount Details
          _buildAmountRow('Subtotal', bill.subtotal),
          if (double.parse(bill.tipAmount) > 0)
            _buildAmountRow('Tip', bill.tipAmount),
          if (double.parse(bill.totalTax) > 0)
            _buildAmountRow('Tax', bill.totalTax),
          if (double.parse(bill.totalDiscount) > 0)
            _buildAmountRow('Discount', '-${bill.totalDiscount}'),
          if (double.parse(bill.serviceCharge) > 0)
            _buildAmountRow('Service Charge', bill.serviceCharge),

          const Divider(color: Colors.grey),

          _buildAmountRow('Total', bill.totalAmount, isTotal: true),

          const SizedBox(height: 8),

          // Created Date
          Text(
            'Created: ${DateFormat('MMM dd, yyyy HH:mm').format(bill.createdAt)}',
            style: GoogleFonts.poppins(
              color: Colors.white60,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              color: isTotal ? Colors.white : Colors.white70,
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '\$$amount',
            style: GoogleFonts.poppins(
              color: isTotal ? Colors.white : Colors.white70,
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'PAID':
        return Colors.green;
      case 'CANCELLED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
